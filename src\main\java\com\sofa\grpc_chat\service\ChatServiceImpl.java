// package com.sofa.grpc_chat.service;
// import java.util.List;
// import java.util.concurrent.CopyOnWriteArrayList;

// import net.devh.boot.grpc.server.service.GrpcService;

// @GrpcService
// public class ChatServiceImpl extends ChatServiceGrpc.ChatServiceImplBase {

//     private final List<StreamObserver<ChatMessage>> clients = new CopyOnWriteArrayList<>();

//     @Override
//     public StreamObserver<ChatMessage> chatStream(StreamObserver<ChatMessage> responseObserver) {
//         clients.add(responseObserver);

//         return new StreamObserver<>() {
//             @Override
//             public void onNext(ChatMessage chatMessage) {
//                 for (StreamObserver<ChatMessage> client : clients) {
//                     if (client != responseObserver) {
//                         client.onNext(chatMessage);
//                     }
//                 }
//             }

//             @Override
//             public void onError(Throwable t) {
//                 clients.remove(responseObserver);
//             }

//             @Override
//             public void onCompleted() {
//                 clients.remove(responseObserver);
//                 responseObserver.onCompleted();
//             }
//         };
//     }
// }